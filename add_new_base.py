# 添加新的和底包的配置
import os
import sys
import json
import shutil
import subprocess
from optparse import OptionParser

## 增加新包配置项目
def write_version_config(version_config_path, commit):
    if not os.path.exists(version_config_path):
        exit('write_version_config version_config_path not exists')
        return
    android_new_version = None
    ios_new_version = None
    version_config = None
    # 读取配置文件
    with open(version_config_path, 'r') as f:
        config = json.load(f)
        version_config = config
        config_len = len(config)
        android_list = config[config_len - 1]['android']
        android_new_version = ''.join(android_list[-1][:-3]) + str(int(android_list[-1][-3:]) + 1)
        ios_list = config[config_len - 1]['ios']
        ios_new_version = ''.join(ios_list[-1][:-3]) + str(int(ios_list[-1][-3:]) + 1)
        print(android_new_version, ios_new_version)
    
    version_config.append({
        'base_commit': commit,
        'android': [android_new_version],
        'ios': [ios_new_version]
    })
    with open(version_config_path, 'w') as f:
        json.dump(version_config, f, ensure_ascii=False, indent=4)

def get_last_commit(version_config_path):
    if not os.path.exists(version_config_path):
        exit('get_last_commit version_config_path not exists')
        return
    
    last_commit = None
    with open(version_config_path, 'r') as f:
        config = json.load(f)
        config_len = len(config)
        last_commit = config[config_len - 1]['base_commit']
    return last_commit

def add_version_config_multi(config_multi_path, version_config_path, commit):
    if not os.path.exists(config_multi_path):
        exit('add_version_config_multi config_multi_path not exists')
        return
    if not os.path.exists(version_config_path):
        exit('add_version_config_multi version_config_path not exists')
        return
    
    last_commit = get_last_commit(version_config_path)
    print(last_commit)

    last_config_multi = []
    old_config_multi = []
    with open(config_multi_path, 'r') as f:
        config = json.load(f)
        old_config_multi = config
        for item in config:
            if last_commit == item['base_commit']:
                last_config_multi.append(item)

    print('增加前：', len(old_config_multi))
    for item in last_config_multi:
        new_item = {'base_commit': commit, 'package_name': '', 'base_version': '', 'versions': []}
        new_item['package_name'] = item['package_name']
        base_version = str(int(item['versions'][0]['num'])+1)
        new_item['base_version'] = base_version
        print(new_item)
        old_config_multi.append(new_item)

    print('增加后：', len(old_config_multi))
    with open(config_multi_path, 'w') as f:
        json.dump(old_config_multi, f, ensure_ascii=False, indent=4)
        

def main():
    parser = OptionParser()
    parser.add_option('-c', '--commit', default='', dest='commit', help='commit id')
    (opts, args) = parser.parse_args()
    print(opts.commit)

    add_version_config_multi('./../hotupdate_history_multi.json', './../version_config_multi.json', opts.commit)
    add_version_config_multi('./../astc_hotupdate_history_multi.json', './../astc_version_config_multi.json', opts.commit)

    write_version_config('./../version_config_multi.json', opts.commit)
    write_version_config('./../astc_version_config_multi.json', opts.commit)

if __name__ == '__main__':
    main()