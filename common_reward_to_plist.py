from PyTexturePacker import Packer
import plistlib
import os, sys, shutil
from PIL import Image

def scale_images(directory, scale):
    for filename in os.listdir(directory):
        if filename.endswith(".jpg") or filename.endswith(".png"):
            img = Image.open(os.path.join(directory, filename))
            width, height = img.size
            img = img.resize((int(width*scale), int(height*scale)))
            img.save(os.path.join(directory, filename))

def pack_test(input_path, output_path, plist_name):
    # create a MaxRectsBinPacker
    packer = Packer.create(max_width=2048, max_height=2048, border_padding=1, enable_rotated=True, trim_mode=5)
    # pack texture images under directory "test_case/" and name the output images as "test_case".
    # "%d" in output file name "test_case%d" is a placeholder, which is the atlas index, starting with 0.

    #input_base_path是input_path的上一级目录
    input_base_path = '/'.join(input_path.split('/')[:-1])
    packer.pack(input_path, plist_name, output_path=output_path, input_base_path=input_base_path)

def common_reward_to_plist(plist_file_path, common_reward_png_dir, add_png_dir=None, sub_png_dir=None):

    plist_name = os.path.splitext(os.path.split(plist_file_path)[1])[0]

    out_path = "out_put_plist"
    if os.path.exists(out_path):
        shutil.rmtree(out_path)
    os.makedirs(out_path)

    low_dir = "low"
    if os.path.exists(low_dir):
        shutil.rmtree(low_dir)
    os.makedirs(low_dir)

    ## 读取plist文件找出需要压缩的所有图片
    plist_data = plistlib.load(open(plist_file_path, 'rb'))
    frames = plist_data['frames']
    for frame in frames:
        if frame.endswith('.png'):
            #frame现在是"low/a.png"这种形式，需要去掉low目录
            frame = os.path.split(frame)[-1]
            png_path = os.path.join(common_reward_png_dir, frame)
            if not os.path.exists(png_path):
                print('\033[31m\033[01mnot found png:\033[0m', '\033[31m' + png_path + '\033[0m')  # 红色加粗错误提示
                continue
            shutil.copy(png_path, low_dir)

    # 如果有新增的图片，也需要加进来
    if add_png_dir:
        for filename in os.listdir(add_png_dir):
            if filename.endswith('.png'):
                png_path = os.path.join(add_png_dir, filename)
                print('add:', filename)
                shutil.copy(png_path, low_dir)
                
    # 如果有需要剔除的图片，也需要剔除
    if sub_png_dir:
        for filename in os.listdir(sub_png_dir):
            if filename.endswith('.png'):
                png_path = os.path.join(low_dir, filename)
                if os.path.exists(png_path):
                    print('sub:', filename)
                    os.remove(png_path)

    # 将所有的图片缩放到原来的一半
    scale_images(low_dir, 0.5)

    #合成plist
    pack_test(low_dir, out_path, plist_name)

    if os.path.exists(low_dir):
        shutil.rmtree(low_dir)


if __name__ == '__main__':
    #使用方法：python3 common_reward_to_plist.py plist_file_path（plist文件路径） common_reward_png_dir(原始png图片目录) add_png_dir(新增图片的目录，默认没有) sub_png_dir(需要剔除的图片目录，默认没有)
    #例子：python3 common_reward_to_plist.py ./../theme_res/810007/res/inner_download/common_reward/common_reward_plist.plist ./../theme_res/999966/res/inner_download/common_reward ./add ./sub
    args = sys.argv[1:]
    if len(args) == 2:
        common_reward_to_plist(args[0], args[1])
    elif len(args) == 3:
        common_reward_to_plist(args[0], args[1], args[2])
    elif len(args) == 4:
        common_reward_to_plist(args[0], args[1], args[2], args[3])
    else:
        print('usage: python common_reward_to_plist.py plist_file_path common_reward_png_dir add_png_dir sub_png_dir')
