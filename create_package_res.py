# coding=UTF-8 删除app资源使用（查找app内没有用的资源）
#先把所有的热更包合并一下，合并成一个完整的资源代码目录，用这个目录找资源

import os
import re
import shutil
import filecmp
import sys,json,hashlib

update_prj_path = os.getenv('SLOTS_DIR')#热更目录
sertch_file_list = ['hotupdate_history']#, 'hotupdate_history_popup'] 先校验部分包
res_result = []
footer_package_list = ['999913','999960','999978','999944','999961']

out_path = os.path.join('/'.join(os.path.split(update_prj_path)[:-1]), 'check_package')

#获取路径
def get_package_path(package_name):
    base_path = ''
    if package_name == 'src':
        base_path = 'src_android'
    elif package_name == 'res':
        base_path = 'res_ios'
    elif package_name == 'rescm':
        base_path = 'res_cm'
    elif package_name.startswith('multilang_force'):
        lang = package_name[-2:]
        base_path = 'multi_lang_res/force/lang_%s/res' % lang 
    elif package_name.startswith('P') or package_name.startswith('CM'):
        if package_name.endswith('ML'):
            base_path = os.path.join('popup_res', package_name[:-2], 'multi')
        else:
            base_path = os.path.join('popup_res', package_name, 'base')
    elif(package_name.endswith('_src') and os.path.isdir()):
        base_path = 'theme_src'
    else:
        base_path = 'theme_res/%s/res' % package_name
    
    return base_path

#是否是本次有更新的包
def is_create_package(package_name, base_commit):
    result = False
    base_path = get_package_path(package_name)
        
    cmd = 'git diff %s HEAD --name-only -- %s' % (base_commit, base_path)
    output = os.popen(cmd).read()
    if output:
        file_list = output.split('\n')
        for file in file_list:
            if base_path in file:
                result = True
                break

    return result

#合并代码
def create_pack_file(prj_path, out_path):

    #文件夹不存在创建
    if(os.path.isdir(out_path)):
        shutil.rmtree(out_path)
    os.mkdir(out_path)

    latest_tag_id = get_latest_tag_id()
    for name in sertch_file_list:
        pakage_list = readFile(name+'.json')

        root = None
        if name == 'hotupdate_history':
            root = update_prj_path
        else:
            root = os.path.join(update_prj_path, 'popup_res')

        for package in pakage_list:
            path = root
            temp = 'res'
            pack_name = package
            base_commit = ''
            content = 'local data = {\n'
            if name == 'hotupdate_history':
                pack_name = package["package_name"]
                if len(package['versions']) > 0 and package['versions'][0]['commit_id'].endswith('_tmp'):
                    del package['versions'][0]

                if len(package['versions']) > 0:
                    base_commit = package['versions'][0]['commit_id']
                else:
                    base_commit = package['base_commit']
                if '_src' in pack_name:
                    path = os.path.join(path, "theme_src", pack_name[:-4])
                    temp = 'src'
                    continue
                else:
                    path = os.path.join(path, "theme_res", pack_name)
            else:
                if pack_name.endswith('ML'):
                    path = os.path.join(path, pack_name[:-2], 'multi')
                else:
                    path = os.path.join(path, pack_name, 'base')

                if len(pakage_list[package]['versions']) == 1 and pakage_list[package]['versions'][0]['tag_id'] == latest_tag_id:
                    base_commit = 'ef61cb201dc0ffba03e802411bf75cf6378dc26d'
                elif len(pakage_list[package]['versions']) > 1 and pakage_list[package]['versions'][0]['tag_id'] == latest_tag_id:
                    del pakage_list[package]['versions'][0]
                    base_commit = pakage_list[package]['versions'][0]['commit_id']
                else:
                    base_commit = pakage_list[package]['versions'][0]['commit_id']

            if pack_name not in footer_package_list:
                continue
                
            #如果没有包直接生成，如果有包的话有更新才会生成
            base_path = get_package_path(pack_name)
            if not os.path.exists(os.path.join(path, 'res/check_package'+pack_name)):
                base_commit = 'ef61cb201dc0ffba03e802411bf75cf6378dc26d'

            if is_create_package(pack_name, base_commit) == False:
                continue
            
            print('生成包：'+pack_name)
            if os.path.exists(path):
                res_path = None
                for maindir, subdir, file_name_list in os.walk(path):
                    #遍历文件
                    for filename in file_name_list:

                        file_path = os.path.join(maindir, filename)
                        if filename == '.DS_Store' or file_path.find('/'+temp+'/') == -1 or 'check_package' in file_path: 
                            continue

                        if not res_path:
                            res_path = file_path[:file_path.find('/'+temp+'/')+len('/'+temp)]
                        
                        last_path = file_path[file_path.find('/'+temp+'/')+1:]
                        list_path = last_path.split('/')[1:]
                        last_path = '/'.join(list_path)
                        md5 = getFileMd5(file_path)
                        one_content = '"' + last_path + ':' + md5 + '",\n'
                        content = content + one_content
                
                content = content[:-2]
                content = content + '\n}\nreturn data'
                writeFile(pack_name, content)

                if temp == 'res':
                    copyToPackagePath(pack_name, res_path)

def get_latest_tag_id():
    output = os.popen('git tag -l "h*"').read()
    tags = [int(float(elem[1:])*1000 + 0.1) for elem in output.split('\n')[:-1]]
    tags.sort(reverse=True)
    return tags[0]

def readFile(file_name):
    root = os.getcwd()
    content = ''
    with open(file_name, 'r') as f:
        content = f.read()
        f.close()
    return json.loads(content)

def writeFile(file_name, content):
    with open(os.path.join(out_path, file_name), 'w') as f:
        f.write(content)
        f.close()

def copyToPackagePath(pack_name, path):
    if os.path.isdir(path):
        check_path = os.path.join(path, 'check_package')
        if not os.path.isdir(check_path):
            os.makedirs(check_path)
        src_path = os.path.join(out_path, pack_name)
        shutil.copy(src_path, check_path)


def getFileMd5(file_path):
    md5 = ''
    with open(file_path, 'rb') as f:
        md5obj = hashlib.md5()
        md5obj.update(f.read())
        md5 = md5obj.hexdigest()
    return md5

def create_pack():
    create_pack_file(update_prj_path, out_path)

def main():
    create_pack_file(update_prj_path, out_path)

if __name__ == '__main__':
    main()