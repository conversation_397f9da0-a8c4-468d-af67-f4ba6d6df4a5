# ! coding=utf-8
import sys, os, shutil, re
import requests, json
from optparse import OptionParser

# 脚本功能：将更新信息上传到群组里面给运维查看

PROJ_PATH = os.getenv('SLOTS_DIR')

def extract_version_from_lua_config(file_path):
    try:
        with open(file_path, 'r', encoding='utf-8') as file:
            content = file.read()
            
            # Use regular expression to find the version string
            pattern = r'Config\.versionStr\s*=\s*"([^"]+)"'
            match = re.search(pattern, content)
            
            if match:
                version = match.group(1)
                return version
            else:
                return 'None'
    except Exception as e:
        print(f"Error reading file: {e}")
        return 'None' 
    
def get_ios_version():
    path = os.path.join(PROJ_PATH, 'version_config_multi.json')
    with open(path) as fd:
        try:
            version_config_tmp = json.loads(fd.read())
            count = len(version_config_tmp)
            temp = version_config_tmp[count-1]['ios'][0]
            return temp[-4:]
        except:
            print('json文件：'+path+'有语法错误！')
            exit()

def get_version_from_http(ios_ver):
    url = "https://d2rargdj3vlbg6.cloudfront.net/sea/dafu888/dafu888_ios_%s.md5" % (ios_ver)
    response = requests.get(url)
    if response.status_code == 200:
        return response.text[:-15]
    else:
        return "None"

def send_word_operate(hot_version, name='无名氏', info=''):
    ios_ver = get_ios_version()
    code_version = get_version_from_http(ios_ver)
    print(code_version)

    code_version32 = get_version_from_http(ios_ver+'_src32')
    print(code_version32)

    code_version64 = get_version_from_http(ios_ver+'_src64')
    print(code_version64)

    # info = info.replace('\n', '<br/>')
    content = "线上前端更新\n更新版本：%s\n代码版本：%s\n32位代码版本：%s\n64位代码版本：%s\n更新负责人：%s\n更新内容：\n%s" % (hot_version, code_version, code_version32, code_version64, name, info)
    print(content)
    requests.post('http://35.86.27.153:9330/api/alerts/public/custom/trigger',
        {"key":"线上更新通报","content":content})


if __name__ == '__main__':
    parser = OptionParser()
    parser.add_option('-i', '--infomation', default='', dest='info', help='update infomation which will be post to dingtalk after build.')
    parser.add_option('-n', '--name', default='无名氏', dest='name', help='update name which will be post to dingtalk after build.')
    (opts, args) = parser.parse_args()
    print(opts.info)
    path = os.path.join(PROJ_PATH, 'src_android/', 'config.lua')
    hot_version = extract_version_from_lua_config(path)
    print(hot_version)
    send_word_operate(hot_version, opts.name, opts.info)
