# coding=UTF-8

import os
import shutil
import zipfile
import filecmp
import json
import time
import sys

#比较两个文件夹下的文件，把不同的文件拷贝出来
no_res = ['inner_download']#不需要拷贝的文件夹
prj_path = '/Users/<USER>/work/Slots888Plus'#工程目录
old_prj_path = '/Users/<USER>/work/Slots888_update'#需要比较的目录
out_path = '/Users/<USER>/work/out_put'#输出目录

no_file = ['android_config.plist', 'config.lua', 'config.plist','ios_config.plist', 'profiler.lua', 'ServerConfig.lua', '.DS_Store','cclog.info']#不需要拷贝的文件

base_commint_id = 'cac1747b94b0e55b5ac1d8e85107202920eb622b'#需要比较的commitid

#创建输出目录
if os.path.isdir(out_path):
    shutil.rmtree(out_path)

if not os.path.isdir(out_path):
	os.mkdir(out_path)

def dir_join_zipPng(list, dir):
    for maindir, subdir, file_name_list in os.walk(dir):
         for filename in file_name_list:
            firstDir = os.path.join(maindir, filename)
            list.append(firstDir)


def zipPngDir(dir):
    zipPng = []
    #压缩图片
    for dirpath, dirnames, filenames in os.walk(dir):  
        for file in filenames :  
            if os.path.splitext(file)[1] == '.png':  
                zipPng.append(os.path.join(dirpath, file))  

    os.chdir('/Applications/ImageAlpha.app/Contents/MacOS')
    for a in range(0,len(zipPng)):
        shell = './pngquant --force --ext .png '+zipPng[a]
        print(shell)
        os.system(shell)

def get_latest_commit_id():
    cmd = 'git rev-parse HEAD'
    output = os.popen(cmd).read()
    return output.strip()

def seach_add_file(prj_path, old_commint_id, out_path):

    root = os.getcwd()
    git_path = os.path.split(prj_path)[0]
    os.chdir(git_path)

    commint_id = get_latest_commit_id()

    #文件夹不存在创建
    if(os.path.isdir(out_path)):
        os.rmdir(out_path)
    os.mkdir(out_path)
    dir = os.path.split(prj_path)[1]
    cmd = 'git diff %s %s --name-only -- %s' % (old_commint_id, commint_id, dir)
    output = os.popen(cmd).read()
    if output:
        file_list = output.split('\n')
        for file in file_list:
            srcDir = os.path.join(git_path, file)
            if os.path.exists(file):

                is_conti = False
                for res_name in no_res:
                    if(res_name in file):
                        is_conti = True
                        break

                if is_conti:
                    continue

                # print("添加文件："+file)

                #文件夹不存在创建
                temp = ('/').join(file.split('/')[1:])
                dscDir = os.path.join(out_path, temp)
                listDir = temp.split('/')
                listDir = listDir[:-1]
                path = out_path
                for value in listDir:
                    path = os.path.join(path, value)
                    if(not os.path.isdir(path)):
                        os.mkdir(path)
                print('copy file: '+srcDir+' to '+dscDir)
                shutil.copy(srcDir,dscDir)

    os.chdir(root)

def compareDifFile(prj_path, old_path):

    prj_diff_file = prj_path
    out_path_file = old_path

    appendPath = []
    for maindir, subdir, file_name_list in os.walk(prj_diff_file):
        
        is_conti = False
        for res_name in no_res:
            if(res_name in maindir):
                is_conti = True
                break

        if is_conti:
            continue

        #遍历文件夹
        for onedir in subdir:
            old_path_file = maindir.replace(prj_path, old_path)
            old_path_dir = os.path.join(old_path_file, onedir)
            #如果工程里面有老版本没有的文件夹则复制过来
            if not os.path.isdir(old_path_dir):
                srcDir = os.path.join(maindir,onedir)

                fpath = srcDir.replace(prj_diff_file, '')
                if fpath[0:1] == '/':
                    fpath = fpath[1:]

                dscDir = os.path.join(out_path_file,fpath)

                bcopy = True
                for value in appendPath:
                    if(srcDir.find(value) != -1):
                        bcopy = False
                        break
                if(bcopy == True):
                    appendPath.append(srcDir)
                    print("拷贝文件夹："+srcDir+":::"+dscDir)
                    shutil.copytree(srcDir, dscDir)

        #遍历文件
        for filename in file_name_list:

            is_conti = False
            for res_name in no_file:
                if(res_name in filename):
                    is_conti = True
                    break

            if is_conti:
                continue
            
            firstDir = os.path.join(maindir, filename)
            bcopy = True
            for value in appendPath:
                if(firstDir.find(value) != -1):
                    bcopy = False
                    break
            
            #新加入的文件
            if(bcopy == True):
                lastVersionDir = maindir.replace(prj_path, old_path)
                lastDir = os.path.join(lastVersionDir,filename)
                if(not os.path.isfile(lastDir)):

                    dscDir = out_path_file

                    srcDir = os.path.split(firstDir)[0]
                    fpath = srcDir.replace(prj_diff_file,'')

                    #文件夹不存在创建
                    listDir = fpath.split('/')
                    path = dscDir
                    for value in listDir:
                        path = os.path.join(path, value)
                        if(not os.path.isdir(path)):
                            os.mkdir(path)

                    if fpath[0:1] == '/':
                        fpath = fpath[1:]
                    dscDir = os.path.join(dscDir,fpath)

                    print("拷贝没有的文件："+ firstDir+":::"+ dscDir)
                    shutil.copy(firstDir,dscDir)

                else:	# 大版本跟小版本不一样
                    if(not filecmp.cmp(firstDir,lastDir)):
                        dscDir = out_path_file

                        srcDir = os.path.split(firstDir)[0] + '/'
                        file = os.path.split(firstDir)[1]
                        fpath = srcDir.replace(prj_diff_file,'')

                        #文件夹不存在创建
                        listDir = fpath.split('/')
                        path = dscDir
                        for value in listDir:
                            path = os.path.join(path, value)
                            if(not os.path.isdir(path)):
                                os.mkdir(path)

                        dscDir = os.path.join(path,file)

                        print("拷贝不一样的文件："+ firstDir+":::"+ dscDir)
                        shutil.copy(firstDir,dscDir)



#改luac
def change_luac(out_path):
    #修改成luac
    for path,dirnames,filenames in os.walk(out_path):
            for filename in filenames:
                if filename[-3:] == 'lua':
                    fileReName = filename+'c'
                    os.rename(os.path.join(path, filename),os.path.join(path, fileReName))

#删除游戏代码
def delete_theme_src(src_path):
    no_delete_list = ['ThemePenguinQuest', 'ThemeCow', 'ThemeCommon', 'ThemeTNT']#这个游戏留在原包中
    for maindir, subdir, file_name_list in os.walk(src_path):
        #遍历文件夹
        for onedir in subdir:
            srcDir = os.path.join(maindir,onedir)

            bcopy = True
            for value in no_delete_list:
                if(srcDir.find(value) != -1):
                    bcopy = False
                    break
            if(bcopy == True and 'Themes/Theme' in srcDir):
                print("删除文件夹："+srcDir)
                shutil.rmtree(srcDir)

#写入commintid
def write_commitid(prj_path, suffix = ''):
    root = os.getcwd()
    git_path = os.path.split(prj_path)[0]
    os.chdir(git_path)
    commitid = get_latest_commit_id()
    os.chdir(root)
    with open('new_commitid'+suffix, 'w') as fd:
        fd.write(str(commitid))
        fd.close()



def read_commitid(suffix = ''):
    commitid = ''

    if(os.path.isfile('new_commitid'+suffix)):
        with open('new_commitid'+suffix, 'r') as fd:
            commitid = fd.read()
            fd.close()

    return commitid

def pull_dsc_dir(old_prj_path):
    root = os.getcwd()
    git_path = os.path.split(old_prj_path)[0]
    os.chdir(git_path)
    os.system('git pull')
    os.chdir(root)

def main():
    global prj_path
    global old_prj_path
    obj = sys.argv[1:]
    base_path = prj_path
    old_base_path = old_prj_path

    if len(obj) == 0 or obj[0] == 'src':
        prj_path = os.path.join(base_path, 'src')#工程目录
        old_prj_path = os.path.join(old_base_path, 'src_android')#需要比较的目录
    else:
        prj_path = os.path.join(base_path,'res')#工程目录
        old_prj_path = os.path.join(old_base_path, 'res_ios')#需要比较的目录

    if('res' in prj_path):

        old_commint_id = read_commitid('res')
        if(old_commint_id == ''):
            print('commitid不存在')
            old_commint_id = base_commint_id

        seach_add_file(prj_path, old_commint_id,out_path)
        zipPngDir(out_path)
        pull_dsc_dir(old_prj_path)
        compareDifFile(out_path, old_prj_path)
        write_commitid(prj_path, 'res')
    else:

        old_commint_id = read_commitid('src')
        if(old_commint_id == ''):
            print('commitid不存在')
            old_commint_id = base_commint_id

        seach_add_file(prj_path, old_commint_id,out_path)
        pull_dsc_dir(old_prj_path)
        compareDifFile(out_path, old_prj_path)
        write_commitid(prj_path, 'src')

if __name__ == '__main__':
    main()
		





