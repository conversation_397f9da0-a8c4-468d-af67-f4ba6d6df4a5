# coding=UTF-8

import json
import os
import shutil
import sys

def read_version_config(filename):
    version_config = []
    with open(filename) as fd:
        version_config_tmp = json.loads(fd.read())
    for elem in version_config_tmp:
        version_config.append(elem)

    return version_config


def copy_theme_src(prj_path, update_path, themeid, root):

    theme_config = read_version_config(os.path.join(root,'theme_py/theme_config.json'))

    bCopy = False
    dscDir = None
    if(not themeid):
        for theme in theme_config:
            if(theme['theme_id']):
                srcDir = os.path.join(prj_path, theme['theme_name'])
                dscDir = os.path.join(update_path, str(theme['theme_id'])+'/src/Themes/'+theme['theme_name'])

                if(os.path.isdir(dscDir)):
                    shutil.rmtree(dscDir)

                shutil.copytree(srcDir, dscDir)
                bCopy = True
    else:
        themeid = int(themeid)
        for theme in theme_config:
            if(themeid == theme['theme_id']):
                srcDir = os.path.join(prj_path, theme['theme_name'])
                dscDir = os.path.join(update_path, str(theme['theme_id'])+'/src/Themes/'+theme['theme_name'])

                if(os.path.isdir(dscDir)):
                    shutil.rmtree(dscDir)

                shutil.copytree(srcDir, dscDir)
                bCopy = True
                break

    #修改成luac
    # if(dscDir):
    #     for path,dirnames,filenames in os.walk(dscDir):
    #             for filename in filenames:
    #                 if filename[-3:] == 'lua':
    #                     fileReName = filename+'c'
    #                     os.rename(os.path.join(path, filename),os.path.join(path, fileReName))

    if bCopy == False:
        print('没有该id:'+str(themeid))

if __name__ == '__main__':
    obj = sys.argv[1:]
    root = obj and obj[0] or os.getcwd()
    themeid = None
    if(len(obj) > 1):
        themeid = obj[1]

    path = root
    if len(obj)==0:
        path = os.path.split(root)[0]

    update_path = os.path.join(path, 'theme_src')
    prj_path = os.path.join(path, 'src_android/Themes')

    print('update_path:'+update_path)
    print('prj_path:'+prj_path)

    copy_theme_src(prj_path,update_path, themeid, path)
