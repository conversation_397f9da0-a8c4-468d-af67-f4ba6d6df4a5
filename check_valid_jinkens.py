#快捷打包，只给出包使用
import os, sys
from jenkinsapi.jenkins import <PERSON> as JenkinsAP<PERSON>

def buildJenkins(params):
    # 指定 Jenkins 服务器地址和用户名、密码
    # jenkins_url = 'http://10.18.0.166:8080/'
    jenkins_url = 'http://10.18.0.167:8080/'
    jenkins_username = 'zsl'
    jenkins_password = 'zsl'

    # 创建 Jenkins 对象
    jenkins = JenkinsAPI(jenkins_url, username=jenkins_username, password=jenkins_password)

    # 获取所有任务名称 - 修复显示问题
    all_jobs = jenkins.get_jobs()
    job_names = [job[0] for job in all_jobs]  # 获取任务名称列表
    print("Available Jobs:", job_names)

    job_name = 'test_check_update_valid'
    
    # 检查任务是否存在
    if job_name not in job_names:
        print(f"Error: Job '{job_name}' not found!")
        print("Available jobs are:")
        for name in job_names:
            print(f"  - {name}")
        return False
    
    # 获取所有构建任务列表
    job = jenkins.get_job(job_name)

    # 获取该任务的所有参数定义
    params_def = job.get_params()
    print("Job parameters:", params_def)

    for param_def in params_def:
        param_name = param_def['name']
        print(f"Required parameter: {param_name}")
        if param_name not in params:
            print(f"Missing required parameter: {param_name}")
            return False

    print("Provided parameters:", params)
    # 启动构建，并传递参数值
    next_build_number = job.get_next_build_number()
    print(f"Starting build #{next_build_number}")
    build = job.invoke(build_params=params)
    return True

def getParams(version, end_check):
    params = {}
    params['version'] = version
    params['end_check'] = end_check

    return params

def build(version, end_check):
    buildJenkins(getParams(version, end_check))

def listJenkinsJobs():
    """列出所有可用的Jenkins任务"""
    jenkins_url = 'http://10.18.0.167:8080/'
    jenkins_username = 'zsl'
    jenkins_password = 'zsl'

    try:
        jenkins = JenkinsAPI(jenkins_url, username=jenkins_username, password=jenkins_password)
        all_jobs = jenkins.get_jobs()
        job_names = [job[0] for job in all_jobs]
        
        print("All available Jenkins jobs:")
        for i, name in enumerate(job_names, 1):
            print(f"{i:2d}. {name}")
        
        return job_names
    except Exception as e:
        print(f"Error connecting to Jenkins: {str(e)}")
        return []

def main():
    obj = sys.argv[1:]
    
    # 如果参数是 'list'，则列出所有任务
    if len(obj) == 1 and obj[0] == 'list':
        listJenkinsJobs()
        return
    
    if len(obj) > 2:
        print('flag error!')
        print('Usage: python3 check_valid_jinkens.py <version> [end_check]')
        print('       python3 check_valid_jinkens.py list  # 列出所有Jenkins任务')
        return
    
    if len(obj) == 0:
        print('Missing version parameter!')
        print('Usage: python3 check_valid_jinkens.py <version> [end_check]')
        print('       python3 check_valid_jinkens.py list  # 列出所有Jenkins任务')
        return
    
    version = int(obj[0])
    end_check = ""
    if len(obj) == 2:
        end_check = obj[1]
    if int(version) < 1000:
        print('version error!')
        return
    end_check = str(end_check)
    build(version, end_check)

if __name__ == '__main__':
   main()
