# coding=UTF-8
#更新删除弹窗的列表

import os
import re
import shutil
import filecmp
import sys
import requests, time

update_prj_path = os.getenv('SLOTS_DIR')#热更目录

def readCsb(path):
    file_str = ''
    if os.path.exists(path):
        with open(path, "rb") as f:
            buf = f.read()
            f.close()
            file_str = bytes.decode(buf,errors='ignore')

    return file_str

def getPathAllCsb(pro):
    all_csb = []
    for path,dirnames,filenames in os.walk(pro):
            for filename in filenames:
                if(filename[-4:] == '.csb'):
                    all_csb.append(os.path.join(path,filename))
    return all_csb

def readDeleteP(pro_path):
    arr = []
    for i in os.listdir(pro_path):
        if(i[0] == 'P'):
            arr.append(i)

    faile_p = []
    for p in arr:
        all_csb = getPathAllCsb(os.path.join(pro_path,p))
        
        for csb in all_csb:
            file_p = readCsb(csb)
            if file_p == '':
                faile_p.append(p)
            pat = re.compile(r'P\d{4}')
            p_arr = pat.findall(file_p)
            p_arr = list(set(p_arr))
            for p_ in p_arr:
                if(p_ != p):
                    faile_p.append(p_)
    faile_p = list(set(faile_p))

    all_arr = []
    for p in arr:
        if p not in faile_p:
            all_arr.append(p[1:])
    all_arr.sort()

    return all_arr

def writeFile(out_path, arr):
    
    content = ',\n'.join(arr)
    with open(os.path.join(out_path, 'PopupBlackList.lua'), 'w') as f:
        f.write('local file_list={\n' + content + '\n}\nreturn file_list')
        f.close()

def getHTMLText(url):
    try:
        r = requests.get(url)
        r.raise_for_status()
        r.encoding = r.apparent_encoding
        return r.text
    except:
        return ""

def getAllPopups():
    arr = []
    #找所文件夹下所有以P开头的文件夹
    path = os.path.join(update_prj_path, 'popup_res')
    for file in os.listdir(path):
        if file.startswith('P'):
            arr.append(file)

    arr.sort(key=lambda x: int(x[1:]))

    arr2 = []
    for key in arr:
        arr2.append("['" + key + "']=true")

    return arr2

def main():

    p_list = readDeleteP(os.path.join(update_prj_path, 'popup_res'))

    user_str = getHTMLText('http://35.155.3.61:8316/get_recent_pops')
    pat = re.compile(r'P\d{4}')
    user_p = pat.findall(user_str)
    user_p = list(set(user_p))

    for i in range(len(user_p)):
        if user_p[i][1:] in p_list:
            p_list.remove(user_p[i][1:])

    writeFile(os.path.join(update_prj_path, 'src_android'), p_list)

    all_arr = getAllPopups()
    content = "return {\n" + ",\n".join(all_arr) + "\n}"
    with open(os.path.join(update_prj_path, 'src_android', 'PopupAllList.lua'), 'w') as f:
        f.write(content)
        f.close()


if __name__ == '__main__':
    main()