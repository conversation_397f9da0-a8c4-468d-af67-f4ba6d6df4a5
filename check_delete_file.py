# coding=UTF-8 删除app资源使用（查找app内没有用的资源）
#先把所有的热更包合并一下，合并成一个完整的资源代码目录，用这个目录找资源

import os
import re
import shutil
import filecmp
import sys
from datetime import datetime

sys.path.append(('./scripts/'))
from bot_lib import send_plain_message

update_prj_path = os.getenv('SLOTS_DIR')#热更目录
sertch_file_list = ['res_ios', 'theme_res', 'multi_lang_res', 'popup_res']
res_result = []

out_path = os.path.join('/'.join(os.path.split(update_prj_path)[:-1]), '.output')

#合并代码
def merge_code(prj_path, out_path):

    #文件夹不存在创建
    if(os.path.isdir(out_path)):
        shutil.rmtree(out_path)
    os.mkdir(out_path)

    #生成一个空文件
    os.system('touch file')
    shutil.move('file', out_path)

    for name in sertch_file_list:
        path = os.path.join(prj_path, name)
        #拷贝app包里的文件
        # if name == 'res_ios':
        #     shutil.copytree(path, os.path.join(out_path, 'res'))
        # el
        if name == 'src_android':
            shutil.copytree(path, os.path.join(out_path, 'src'))
        else:

            #拷贝热更的文件
            temp = 'src'
            if name == 'theme_res' or name == 'multi_lang_res' or name == 'popup_res':
                temp = 'res'

            if name == 'res_ios':
                temp = 'res_ios'

            list_dir = []
            for maindir, subdir, file_name_list in os.walk(path):
                #遍历文件
                for filename in file_name_list:

                    file_path = os.path.join(maindir, filename)
                    if filename == '.DS_Store' or file_path.find('/'+temp+'/') == -1: 
                        continue
                    
                    last_path = file_path[file_path.find('/'+temp+'/')+1:]
                    if name == 'res_ios':
                        last_path = last_path.replace('res_ios', 'res')

                    #没有文件夹的话需要创建
                    list_path = last_path.split('/')[:-1]
                    dir_path = os.path.join(out_path, '/'.join(list_path))
                    if not os.path.exists(dir_path):
                        os.makedirs(dir_path)

                    des_path = os.path.join(out_path, last_path)
                    if not os.path.exists(des_path):
                        file_path = os.path.join(out_path, 'file')
                        shutil.copy(file_path, des_path)

def readFile(file_name):
    content = ''
    with open(file_name, 'r') as f:
        content = f.read()
    return content
    
def isDirNotNull(dir_path):
    for maindir, subdir, file_name_list in os.walk(dir_path):
        #遍历文件
        for filename in file_name_list:
            if filename != '.DS_Store':
                return True

    return False

def main():
    merge_code(update_prj_path, out_path)

    #比较文件
    fail_arr = []

    #查看这个大删除文件是否改变改变再校验
    cmd = 'git log --name-only --pretty=format:%H --since="1 hour ago" -- src_android/deleteFile.lua'
    output = os.popen(cmd).read()
    if output:
        content = readFile(os.path.join(update_prj_path, 'src_android/deleteFile.lua'))
        arr = content.split('\n')
        for line in arr:
            if line.find('BLDownloadRes')!=-1:
                line = re.sub(r'",','', line)
                line = re.sub(r'"BLDownloadRes','res', line)
                file_ext = os.path.splitext(line)
                if file_ext[1] == '.dir':
                    if os.path.isdir(os.path.join(out_path, file_ext[0])) and isDirNotNull(os.path.join(out_path, file_ext[0])):
                        fail_arr.append('dir:'+file_ext[0])
                else:   
                    if os.path.exists(os.path.join(out_path, line)):
                        fail_arr.append(line)

    content = readFile(os.path.join(update_prj_path, 'src_android/quickDeleteFile.lua'))
    arr = content.split('\n')
    for line in arr:
        if line.find('BLDownloadRes')!=-1:
            line = re.sub(r'",','', line)
            line = re.sub(r'"BLDownloadRes','res', line)
            file_ext = os.path.splitext(line)
            if file_ext[1] == '.dir':
                if os.path.isdir(os.path.join(out_path, file_ext[0])) and isDirNotNull(os.path.join(out_path, file_ext[0])):
                    fail_arr.append('dir:'+file_ext[0])
            else:   
                if os.path.exists(os.path.join(out_path, line)):
                    fail_arr.append(line)

    if len(fail_arr)>0:
        print('校验deleteFile.lua文件失败！')
        print('\n'.join(fail_arr))
        print('check_delete_file error end:')
        send_plain_message('校验deleteFile.lua文件错误，请检查！', 'frontend_broadcast')
    else:
        print('校验deleteFile.lua文件成功！')

if __name__ == '__main__':
    main()