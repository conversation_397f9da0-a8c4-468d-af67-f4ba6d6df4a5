import sys, os
sys.path.append(('./'))
from build_resource_config import multi_pack_config

def checkout_to_branch(branch_name):
    os.system('git restore .')
    os.system('git clean -fd')
    os.system('git checkout %s' % branch_name)
    os.system('git pull')

def main():
    obj = sys.argv[1:]
    if(len(obj) != 1):
        print('server_name error!')
        return
    
    server_name = obj[0]
    branch_name = multi_pack_config[server_name]['branch_name']
    checkout_to_branch(branch_name)

if __name__ == '__main__':
    main()