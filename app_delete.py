# coding=UTF-8 删除app资源使用（查找app内没有用的资源）
#先把所有的热更包合并一下，合并成一个完整的资源代码目录，用这个目录找资源

import os
import re
import shutil
import filecmp
import sys, hashlib
from datetime import datetime

update_prj_path = os.getenv('SLOTS_DIR')#热更目录
sertch_file_list = ['res_ios', 'theme_res', 'multi_lang_res', 'popup_res']
BASE_COMMIT_ID = 'a82a7bc87ac717555c0e47e090c6a442a26af7c5'#'855c5ba2fe504edd1fd81c72e41780929b882b60'#'ebb71610722bdf19420b2012a18ebbf23d6f7377'
QUICK_COMMIT_ID = '1c8b640b127092b4d25fa337ea74e24e6e5ccb63'

out_path = os.path.join('/'.join(os.path.split(update_prj_path)[:-1]), '.delete')

def getStrMd5(content):
    md5obj = hashlib.md5()
    md5obj.update(content.encode('utf-8'))
    md5 = md5obj.hexdigest()
    return md5

def getHEADCommitID():
    cmd = 'git rev-parse HEAD'
    output = os.popen(cmd).read()
    if output:
        return output.split('\n')[0]
    return 'HEAD'

def seach_add_file(prj_path, out_path, base_commit_id, end_commit_id):

    res_files = set()
    res_dirs = set()

    root = os.getcwd()
    git_path = os.path.split(prj_path)[0]
    os.chdir(git_path)

    dirname = os.path.split(prj_path)[1]

    flag = 'src'
    if(dirname == 'res_ios' or dirname == 'theme_res' or dirname == 'multi_lang_res' or dirname == 'popup_res'):
        flag = 'res'

    md5_dir = os.path.join(out_path, '.log')
    if not os.path.exists(md5_dir):
        os.makedirs(md5_dir)
    md5_file_name = getStrMd5(dirname + ' ' + base_commit_id + ' ' + end_commit_id)
    md5_path = os.path.join(md5_dir, md5_file_name)

    output = ''
    if os.path.exists(md5_path):
        output = readFile(md5_path)
    else:
        cmd = 'git log %s..%s --diff-filter=D --summary --name-only %s' %(base_commit_id, end_commit_id, dirname)
        output = os.popen(cmd).read()
    if output:
        with open(md5_path, 'w') as f:
            f.write(output)
            f.close()

        file_list = output.split('\n')

        if dirname == 'res_ios' or dirname == 'src_android':
            for file in file_list:
                if(dirname+'/' in file):
                    des_path = file.replace(dirname, flag)
                    des_file_path = os.path.join(out_path, des_path)
                    if not os.path.exists(des_file_path):
                        temp = 'BLDownloadRes'
                        if flag == 'src':
                            temp = 'bolesrc'
                        path = des_file_path.replace(os.path.join(out_path, flag), temp)
                        res_files.add(path)

                        dir_list = path.split('/')[1:-1]
                        base_path = os.path.join(out_path, flag)
                        for dir_path in dir_list:
                            base_path = os.path.join(base_path, dir_path)

                            if not os.path.isdir(base_path):
                                des_path = base_path.replace(os.path.join(out_path, flag), temp)
                                if "\\" not in des_path:
                                    res_dirs.add(des_path)
                                    break
                        
        else:
            for file in file_list:
                if(dirname+'/' in file):

                    if file.find('/'+flag+'/') == -1:
                        continue

                    des_path = file[file.find('/'+flag+'/')+1:]
                    des_file_path = os.path.join(out_path, des_path)
                    if not os.path.exists(des_file_path):
                        temp = 'BLDownloadRes'
                        if flag == 'src':
                            temp = 'bolesrc'
                        path = des_file_path.replace(os.path.join(out_path, flag), temp)
                        res_files.add(path)

                        dir_list = path.split('/')[1:-1]
                        base_path = os.path.join(out_path, flag)
                        for dir_path in dir_list:
                            base_path = os.path.join(base_path, dir_path)

                            if not os.path.isdir(base_path):
                                des_path = base_path.replace(os.path.join(out_path, flag), temp)
                                if "\\" not in des_path:
                                    res_dirs.add(des_path)
                                    break

        os.chdir(root)
        os.rename(md5_path, md5_path+'.use')
    else:
        os.chdir(root)
    return res_files, res_dirs

#合并代码
def merge_code(prj_path, out_path):

    root = os.getcwd()
    os.chdir(prj_path)

    last_commit_id = ''
    if os.path.exists(os.path.join('./theme_py', 'commit_id')):
        with open(os.path.join('./theme_py', 'commit_id'), 'r') as f:
            last_commit_id = f.read().strip('\n')
            f.close()

    result = False
    res_list = {}
    res_change = False
    #没有和过文件就用本地合并
    if last_commit_id == '':
        #文件夹不存在创建
        if(os.path.isdir(out_path)):
            shutil.rmtree(out_path)
        os.mkdir(out_path)

        #生成一个空文件
        os.system('touch file')
        shutil.move('file', out_path)

        for name in sertch_file_list:
            path = os.path.join(prj_path, name)
            #拷贝app包里的文件
            # if name == 'res_ios':
            #     shutil.copytree(path, os.path.join(out_path, 'res'))
            # elif name == 'src_android':
            #     shutil.copytree(path, os.path.join(out_path, 'src'))
            # else:
            #拷贝热更的文件
            temp = 'src'
            if name == 'theme_res' or name == 'multi_lang_res' or name == 'popup_res':
                temp = 'res'

            if name == 'res_ios':
                temp = 'res_ios'
            elif name == 'src_android':
                temp = 'src_android'
                continue

            for maindir, subdir, file_name_list in os.walk(path):
                #遍历文件
                for filename in file_name_list:

                    file_path = os.path.join(maindir, filename)
                    if filename == '.DS_Store' or file_path.find('/'+temp+'/') == -1: 
                        continue
                    
                    last_path = file_path[file_path.find('/'+temp+'/')+1:]
                    if name == 'res_ios':
                        last_path = last_path.replace('res_ios', 'res')

                    #没有文件夹的话需要创建
                    list_path = last_path.split('/')[:-1]
                    dir_path = os.path.join(out_path, '/'.join(list_path))
                    if not os.path.exists(dir_path):
                        os.makedirs(dir_path)

                    des_path = os.path.join(out_path, last_path)
                    if not os.path.exists(des_path):
                        file_path = os.path.join(out_path, 'file')
                        shutil.copy(file_path, des_path)

                    if des_path.find('/res/') != -1:
                        des_path = des_path[des_path.find('/res/')+1:]
                    elif des_path.find('/src/') != -1:
                        des_path = des_path[des_path.find('/src/')+1:]
                    if res_list.get(des_path) == None or res_list[des_path]==0:
                        res_list[des_path] = 1
                    else:
                        res_list[des_path] = res_list[des_path] + 1

        result = True
    else:
        if not os.path.exists(os.path.join(out_path, 'file')):
            #生成一个空文件
            os.system('touch file')
            shutil.move('file', out_path)

        if os.path.exists(os.path.join(out_path, 'res_list')):
            with open(os.path.join(out_path, 'res_list'), 'r') as f:
                content = f.read()
                arr = content[:-1].split('\n')
                for file in arr:
                    index = file.find(':')
                    src = file[:index]
                    count = int(file[index+1:])
                    res_list[src] = count
                f.close()

        #如果和过就用git选出添加的文件合并
        copy_map = {}
        for name in sertch_file_list:
            #拷贝热更的文件
            temp = 'src'
            if name == 'res_ios' or name == 'theme_res' or name == 'multi_lang_res' or name == 'popup_res':
                temp = 'res'
            
            cmd = 'git diff %s %s --name-only %s' % (last_commit_id, 'HEAD', name)
            cmd_status = 'git diff %s %s --name-status %s' % (last_commit_id, 'HEAD', name)
            out_status = os.popen(cmd_status).read()
            status_list = {}
            if out_status:
                file_list = out_status.split('\n')
                for file in file_list:
                    if file[:1] == 'A':
                        src = file.split('\t')[-1]
                        status_list[src] = 'A'
                    elif file[:1] == 'D':
                        src = file.split('\t')[-1]
                        status_list[src] = 'D'
                    elif file[:1] == 'R':#重命名或着文件路径改变
                        src = file.split('\t')[-2]
                        des = file.split('\t')[-1]
                        if src.split('/')[0] == name:
                            status_list[src] = 'D'
                        if des.split('/')[0] == name:
                            status_list[des] = 'A'

                if len(status_list) != 0:
                    res_change = True

                for file in status_list:
                    des = ''
                    if name == 'res_ios' or name == 'src_android':
                        last_temp = file.split(' ')[-1]
                        path_temp = last_temp[last_temp.find(name+'/'):]
                        des = os.path.join(out_path, path_temp)
                        des = des.replace('/'+name+'/', '/'+temp+'/')
                    else:
                        last_temp = file.split(' ')[-1]
                        path_temp = last_temp[last_temp.find(name+'/'):]
                        file_temp = path_temp[path_temp.find('/'+temp+'/')+1:]
                        des = os.path.join(out_path, file_temp)
                    #加文件
                    if status_list.get(file) == 'A':
                        if des.find('/res/') != -1:
                            des = des[des.find('/res/')+1:]
                        elif des.find('/src/') != -1:
                            des = des[des.find('/src/')+1:]
                        if res_list.get(des) == None or res_list[des]<=0:
                            res_list[des] = 1
                        else:
                            res_list[des] = res_list[des] + 1

                #必须整体加入后再减 用来判断是否有错误
                for file in status_list:
                    des = ''
                    if name == 'res_ios' or name == 'src_android':
                        last_temp = file.split(' ')[-1]
                        path_temp = last_temp[last_temp.find(name+'/'):]
                        des = os.path.join(out_path, path_temp)
                        des = des.replace('/'+name+'/', '/'+temp+'/')
                    else:
                        last_temp = file.split(' ')[-1]
                        path_temp = last_temp[last_temp.find(name+'/'):]
                        file_temp = path_temp[path_temp.find('/'+temp+'/')+1:]
                        des = os.path.join(out_path, file_temp)
                    #减文件
                    if status_list.get(file) == 'D':
                        if des.find('/res/') != -1:
                            des = des[des.find('/res/')+1:]
                        elif des.find('/src/') != -1:
                            des = des[des.find('/src/')+1:]
                        if res_list.get(des) == None or res_list[des]<=0:
                            print('calculate count error!'+des)
                            content = 'start: ' + last_commit_id + ' end: ' + getHEADCommitID() + '\nres: ' + des + '\n'
                            writeErrorFile(out_path, 'error', content)
                        else:
                            res_list[des] = res_list[des] - 1

            output = os.popen(cmd).read()
            if output:
                file_list = output.split('\n')
                for file in file_list:
                    if(name+'/' in file):
                        src = ''
                        des = ''
                        if name == 'res_ios' or name == 'src_android':
                            last_temp = file.split(' ')[-1]
                            path_temp = last_temp[last_temp.find(name+'/'):]
                            src = os.path.join(prj_path, path_temp)
                            file_temp = path_temp.replace(name, temp)
                            #没有文件夹的话需要创建
                            list_path = file_temp.split('/')[:-1]
                            dir_path = os.path.join(out_path, '/'.join(list_path))
                            if not os.path.exists(dir_path):
                                os.makedirs(dir_path)

                            des = os.path.join(out_path, path_temp)
                            des = des.replace('/'+name+'/', '/'+temp+'/')
                        else:
                            last_temp = file.split(' ')[-1]
                            path_temp = last_temp[last_temp.find(name+'/'):]
                            src = os.path.join(prj_path, path_temp)
                            file_temp = path_temp[path_temp.find('/'+temp+'/')+1:]
                            #没有文件夹的话需要创建
                            list_path = file_temp.split('/')[:-1]
                            dir_path = os.path.join(out_path, '/'.join(list_path))
                            if not os.path.exists(dir_path):
                                os.makedirs(dir_path)
                            des = os.path.join(out_path, file_temp)
                        man_des = ''
                        if os.path.exists(src) and not os.path.exists(des):
                            copy_map[src] = des
                            result = True
                        elif not os.path.exists(src) and os.path.exists(des):
                            man_des = des
                            if des.find('/res/') != -1:
                                des = des[des.find('/res/')+1:]
                            elif des.find('/src/') != -1:
                                des = des[des.find('/src/')+1:]
                            if res_list.get(des) == None or res_list[des]<0:
                                print('no delete file!'+des)
                            else:
                                if res_list[des] == 0:
                                    del res_list[des]
                                    os.remove(man_des)
                                    parent_dir = os.path.dirname(man_des)
                                    if not getDirHasFiles(parent_dir):
                                        shutil.rmtree(parent_dir)
                            result = True
        
        for src in copy_map:
            des_dir = os.path.split(copy_map[src])[0]
            if not os.path.exists(des_dir):
                os.makedirs(des_dir)
            file_path = os.path.join(out_path, 'file')
            shutil.copy(file_path, copy_map[src])

    #每次有文件变动就需要更新commit_id不然会多次执行某一文件的添加或删除
    if result == True or res_change:
        comite_id = os.popen('git rev-parse HEAD').read()
        with open('./theme_py/commit_id', 'w') as f:
            f.write(comite_id)
            f.close()

    if result == True:
        content = ''
        index = 0
        res_count = len(res_list)
        if os.path.exists(os.path.join(out_path, 'res_list')):
            os.remove(os.path.join(out_path, 'res_list'))

        for src in res_list:
            content = content + src + ':' + str(res_list[src])+'\n'
            index = index + 1
            if index%100 == 0 or res_count == index:
                if os.path.exists(os.path.join(out_path, 'res_list')):
                    with open(os.path.join(out_path, 'res_list'), 'a') as f:
                        f.write(content)
                        f.close()
                else:
                    with open(os.path.join(out_path, 'res_list'), 'w') as f:
                        f.write(content)
                        f.close()

                content = ''

    os.chdir(root)

    return result

def getDirHasFiles(path):
    file_list = os.listdir(path)
    return len(file_list) > 0

def writeFile(out_path, res_files, res_dirs, out_file_name):
    arr = list(res_files)
    arr.sort()
    arr2 = []
    for strname in arr: 
        is_add = True

        dir_list = strname.split('/')[:-1]
        base_path = ''
        for dir_path in dir_list:
            base_path = os.path.join(base_path, dir_path)
            if base_path in res_dirs:
                is_add = False
                break

        if is_add and os.path.splitext(strname)[-1] in ['.png', '.jpg', '.plist', '.mp3', '.skel', '.csb', '.atlas', '.json', '.fnt', '.mp4', '.db', '.ttf', '.TTF', '.txt', '.zip']:
            arr2.append("\""+strname+"\"")

    arr = list(res_dirs)
    arr.sort()
    for dirname in arr:
        arr2.append("\""+dirname+".dir\"")

    content = ',\n'.join(arr2)
    with open(os.path.join(out_path, out_file_name), 'w') as f:
        f.write('local file_list={\n' + content + '\n}\nreturn file_list')
        f.close()

def copeFile(out_path, out_file_name):
    file_path = os.path.join(out_path, out_file_name)
    dst = os.path.join(update_prj_path, 'src_android', out_file_name)
    if os.path.exists(file_path):
        shutil.copy(file_path, dst)

def readFile(file_name):
    content = ''
    with open(file_name, 'r') as f:
        content = f.read()
    return content

def writeErrorFile(out_path, out_file_name, content):
    file_path = os.path.join(out_path, out_file_name)
    if os.path.exists(file_path):
        with open(file_path, 'a') as f:
            f.write(content)
            f.close()
    else:
        with open(file_path, 'w') as f:
            f.write(content)
            f.close()

#删除没有.use后缀的文件
def deleteLogFile(log_path):
    for file in os.listdir(log_path):
        file_path = os.path.join(log_path, file)
        if file_path.endswith('.use'):
            os.rename(file_path, file_path[:-4])
        elif(file != '.DS_Store'):
            os.remove(file_path)

def main():
    result = merge_code(update_prj_path, out_path)
    if result == False:
        print('no need create delete file!')
        return

    res_files = set()
    res_dirs = set()
    for name in sertch_file_list:
        path = os.path.join(update_prj_path, name)
        files, dirs = seach_add_file(path, out_path, BASE_COMMIT_ID, QUICK_COMMIT_ID)

        res_files = res_files | files
        res_dirs = res_dirs | dirs

    writeFile(out_path, res_files, res_dirs, 'deleteFile.lua')
    copeFile(out_path, 'deleteFile.lua')

    #新建快速搜索表
    quick_res_files = set()
    quick_res_dirs = set()
    for name in sertch_file_list:
        path = os.path.join(update_prj_path, name)
        files, dirs = seach_add_file(path, out_path, QUICK_COMMIT_ID, getHEADCommitID())
        quick_res_files = quick_res_files | files
        quick_res_dirs = quick_res_dirs | dirs

    writeFile(out_path, quick_res_files, quick_res_dirs, 'quickDeleteFile.lua')
    copeFile(out_path, 'quickDeleteFile.lua')

    deleteLogFile(os.path.join(out_path, '.log'))

if __name__ == '__main__':
    main()